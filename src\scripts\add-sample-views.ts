import { config } from 'dotenv';
import { db } from '../lib/db';
import { blogs, blogViews } from '../lib/db/schema';

// Load environment variables
config({ path: '.env.local' });

async function addSampleViews() {
  try {
    console.log('🌱 Adding sample blog views...');

    // Get first blog
    const existingBlogs = await db.select().from(blogs).limit(1);
    
    if (existingBlogs.length === 0) {
      console.log('No blogs found.');
      return;
    }

    const blog = existingBlogs[0];
    console.log(`Adding views for blog: ${blog.title}`);

    // Add some sample views
    const sampleViews = [];
    const now = new Date();
    
    // Add views for last 7 days
    for (let i = 0; i < 7; i++) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const viewsPerDay = Math.floor(Math.random() * 20) + 5; // 5-25 views per day
      
      for (let j = 0; j < viewsPerDay; j++) {
        const viewTime = new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000);
        
        sampleViews.push({
          id: `sample-view-${Date.now()}-${i}-${j}`,
          blogId: blog.id,
          userId: Math.random() > 0.7 ? '0af03f1d-b070-4cf8-b29e-b67770eb9623' : null,
          ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0 (Sample Data)',
          device: ['desktop', 'mobile', 'tablet'][Math.floor(Math.random() * 3)],
          country: ['Bangladesh', 'India', 'USA'][Math.floor(Math.random() * 3)],
          isBot: false,
          isUnique: Math.random() > 0.3,
          createdAt: viewTime,
          updatedAt: viewTime,
        });
      }
    }

    // Insert in smaller batches
    const batchSize = 10;
    for (let i = 0; i < sampleViews.length; i += batchSize) {
      const batch = sampleViews.slice(i, i + batchSize);
      await db.insert(blogViews).values(batch);
      console.log(`✅ Inserted batch ${Math.floor(i/batchSize) + 1}`);
    }

    console.log(`🎉 Added ${sampleViews.length} sample views successfully!`);
    
  } catch (error) {
    console.error('❌ Error adding sample views:', error);
  }
}

// Run the function
addSampleViews();
