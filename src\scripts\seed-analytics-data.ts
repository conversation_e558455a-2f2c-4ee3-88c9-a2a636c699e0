import { config } from 'dotenv';
import { db } from '../lib/db';
import { blogs, blogViews, blogMonetization } from '../lib/db/schema';
import { eq } from 'drizzle-orm';

// Load environment variables
config({ path: '.env.local' });

async function seedAnalyticsData() {
  try {
    console.log('🌱 Starting to seed analytics data...');

    // First, let's check if there are any existing blogs
    const existingBlogs = await db.select().from(blogs).limit(5);
    
    if (existingBlogs.length === 0) {
      console.log('No blogs found. Creating sample blogs first...');
      
      // Create sample blogs (you'll need to replace with actual user ID)
      const sampleBlogs = [
        {
          id: 'blog-1',
          title: 'Getting Started with Next.js',
          slug: 'getting-started-with-nextjs',
          content: 'This is a comprehensive guide to Next.js...',
          excerpt: 'Learn the basics of Next.js framework',
          authorId: '0af03f1d-b070-4cf8-b29e-b67770eb9623', // Replace with actual user ID
          status: 'published' as const,
          viewCount: 0,
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-15'),
        },
        {
          id: 'blog-2',
          title: 'Advanced React Patterns',
          slug: 'advanced-react-patterns',
          content: 'Explore advanced React patterns and techniques...',
          excerpt: 'Master advanced React development patterns',
          authorId: '0af03f1d-b070-4cf8-b29e-b67770eb9623', // Replace with actual user ID
          status: 'published' as const,
          viewCount: 0,
          createdAt: new Date('2024-01-20'),
          updatedAt: new Date('2024-01-20'),
        },
        {
          id: 'blog-3',
          title: 'Database Design Best Practices',
          slug: 'database-design-best-practices',
          content: 'Learn how to design efficient databases...',
          excerpt: 'Essential database design principles',
          authorId: '0af03f1d-b070-4cf8-b29e-b67770eb9623', // Replace with actual user ID
          status: 'published' as const,
          viewCount: 0,
          createdAt: new Date('2024-01-25'),
          updatedAt: new Date('2024-01-25'),
        }
      ];

      for (const blog of sampleBlogs) {
        await db.insert(blogs).values(blog);
        console.log(`✅ Created blog: ${blog.title}`);
      }
    }

    // Get all blogs to add views to
    const allBlogs = await db.select().from(blogs).limit(10);
    console.log(`Found ${allBlogs.length} blogs to add analytics data to`);

    // Generate sample blog views for each blog
    for (const blog of allBlogs) {
      console.log(`Adding analytics data for: ${blog.title}`);
      
      // Generate views for the last 30 days
      const viewsData = [];
      const now = new Date();
      
      for (let i = 0; i < 30; i++) {
        const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
        const viewsPerDay = Math.floor(Math.random() * 50) + 5; // 5-55 views per day
        
        for (let j = 0; j < viewsPerDay; j++) {
          const isUnique = Math.random() > 0.3; // 70% unique views
          const devices = ['desktop', 'mobile', 'tablet'];
          const countries = ['Bangladesh', 'India', 'USA', 'UK', 'Canada', 'Australia'];
          
          viewsData.push({
            id: `view-${blog.id}-${i}-${j}`,
            blogId: blog.id,
            userId: Math.random() > 0.5 ? '0af03f1d-b070-4cf8-b29e-b67770eb9623' : null,
            ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
            userAgent: 'Mozilla/5.0 (compatible; Analytics Seeder)',
            device: devices[Math.floor(Math.random() * devices.length)],
            country: countries[Math.floor(Math.random() * countries.length)],
            isBot: false,
            isUnique,
            createdAt: new Date(date.getTime() + Math.random() * 24 * 60 * 60 * 1000),
            updatedAt: new Date(),
          });
        }
      }

      // Insert views in batches
      const batchSize = 100;
      for (let i = 0; i < viewsData.length; i += batchSize) {
        const batch = viewsData.slice(i, i + batchSize);
        await db.insert(blogViews).values(batch);
      }
      
      console.log(`✅ Added ${viewsData.length} views for ${blog.title}`);

      // Add monetization data for some blogs
      if (Math.random() > 0.5) {
        const monetizationData = {
          id: `monetization-${blog.id}`,
          blogId: blog.id,
          status: 'approved' as const,
          totalEarnings: (Math.random() * 100).toFixed(2),
          uniqueReads: Math.floor(Math.random() * 1000) + 100,
          cprRate: (Math.random() * 0.1 + 0.05).toFixed(3),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        await db.insert(blogMonetization).values(monetizationData);
        console.log(`✅ Added monetization data for ${blog.title}`);
      }
    }

    console.log('🎉 Analytics data seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error seeding analytics data:', error);
  }
}

// Run the seeding function
seedAnalyticsData();
